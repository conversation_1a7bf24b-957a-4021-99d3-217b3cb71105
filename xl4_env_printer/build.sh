#!/bin/bash

# Build script for EnvPrinter Docker image
set -e

# Configuration
IMAGE_NAME="xl4-env-printer"
TAG="latest"
REGION="us-east-1"
ACCOUNT_ID="${AWS_ACCOUNT_ID:-************}"
ECR_REPOSITORY="${ACCOUNT_ID}.dkr.ecr.${REGION}.amazonaws.com/${IMAGE_NAME}"

echo "🚀 Building EnvPrinter Docker Image"
echo "=================================="
echo "Image Name: ${IMAGE_NAME}"
echo "Tag: ${TAG}"
echo "ECR Repository: ${ECR_REPOSITORY}"
echo

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Build the Docker image
echo "📦 Building Docker image..."
docker build -t ${IMAGE_NAME}:${TAG} .

if [ $? -eq 0 ]; then
    echo "✅ Docker image built successfully!"
else
    echo "❌ Docker build failed!"
    exit 1
fi

# Tag for ECR
echo "🏷️  Tagging image for ECR..."
docker tag ${IMAGE_NAME}:${TAG} ${ECR_REPOSITORY}:${TAG}

echo "✅ Build completed successfully!"
echo
echo "📋 Next steps:"
echo "1. Login to ECR: aws ecr get-login-password --region ${REGION} | docker login --username AWS --password-stdin ${ECR_REPOSITORY}"
echo "2. Create ECR repository (if not exists): aws ecr create-repository --repository-name ${IMAGE_NAME} --region ${REGION}"
echo "3. Push image: docker push ${ECR_REPOSITORY}:${TAG}"
echo "4. Update template.yaml parameter EnvPrinterDockerImageUrl with: ${ECR_REPOSITORY}:${TAG}"
echo

# Optional: Run the image locally for testing
read -p "🧪 Do you want to test the image locally? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧪 Running image locally for testing..."
    docker run --rm \
        -e AWS_DEFAULT_REGION=${REGION} \
        -e TASK_TYPE=test \
        -e MESSAGE="Local test run" \
        -e CHAIN_STEP=0 \
        ${IMAGE_NAME}:${TAG}
fi

echo "🎉 Build script completed!"
