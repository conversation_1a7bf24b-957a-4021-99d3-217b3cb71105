# EnvPrinter Docker Image

A custom Docker image for the ECS task that prints environment variables, lists running ECS jobs, and shows S3 bucket contents.

## Features

- 📋 **Environment Variable Display**: Shows all environment variables organized by category
- 🏃 **ECS Task Listing**: Lists all running tasks in the specified ECS cluster
- 📁 **S3 Bucket Contents**: Shows files in the specified S3 bucket
- 🔒 **Security**: Masks sensitive environment variables
- 📊 **Detailed Output**: Provides comprehensive information about tasks and files

## Environment Variables

### Required
- `AWS_DEFAULT_REGION`: AWS region (e.g., 'us-east-1')

### Optional
- `ECS_CLUSTER_NAME`: Name of the ECS cluster to list tasks from
- `S3_BUCKET_NAME`: Name of the S3 bucket to list files from
- `TASK_TYPE`: Type identifier for the task (default: 'env_printer')
- `CHAIN_STEP`: Step number in a task chain
- `PREVIOUS_TASK_ID`: ID of the previous task in a chain
- `MESSAGE`: Custom message to display

## Building the Image

1. **Build locally:**
   ```bash
   cd xl4_env_printer
   ./build.sh
   ```

2. **Push to ECR:**
   ```bash
   # Login to ECR
   aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 588846411770.dkr.ecr.us-east-1.amazonaws.com

   # Create repository (if not exists)
   aws ecr create-repository --repository-name xl4-env-printer --region us-east-1

   # Push image
   docker push 588846411770.dkr.ecr.us-east-1.amazonaws.com/xl4-env-printer:latest
   ```

## Usage in Task Chains

The EnvPrinter is designed to be used in ECS task chains. Example configuration:

```ruby
task_chain = [
  {
    'type' => 'env_printer',
    'task_definition' => ENV['ECS_TASK_DEFINITION_ENV_PRINTER'],
    'container_name' => 'EnvPrinter',
    'environment_vars' => [
      { name: 'MESSAGE', value: 'First chained task' },
      { name: 'CHAIN_STEP', value: '1' },
      { name: 'S3_BUCKET_NAME', value: 'my-bucket' }
    ]
  }
]
```

## Sample Output

```
================================================================================
🚀 ENVIRONMENT PRINTER TASK STARTED
================================================================================
Timestamp: 2024-12-01T10:30:00Z
Region: us-east-1
Cluster: DeltaServerECSCluster
S3 Bucket: my-download-bucket

📋 ENVIRONMENT VARIABLES:
----------------------------------------
  AWS Configuration:
    AWS_DEFAULT_REGION = us-east-1
    AWS_EXECUTION_ENV = AWS_ECS_FARGATE

  ECS Configuration:
    ECS_CLUSTER_NAME = DeltaServerECSCluster

  Task Chain Variables:
    TASK_TYPE = env_printer
    CHAIN_STEP = 1
    PREVIOUS_TASK_ID = task-1234

🏃 RUNNING ECS TASKS:
----------------------------------------
  Found 2 running task(s):

  1. Task ID: task-5678
     Task Definition: DeltaServerTaskDefinitionScale2:1
     CPU/Memory: 16384/32768
     Created: 2024-12-01T10:25:00Z
     Last Status: RUNNING
     Containers:
       - DeltaExecutorScale2: RUNNING

📁 S3 BUCKET CONTENTS:
----------------------------------------
  Found 5 file(s) in bucket 'my-download-bucket':

  1. delta-output-20241201.zip
     Size: 15.67 MB
     Last Modified: 2024-12-01T10:20:00Z
     Storage Class: STANDARD

✅ TASK COMPLETION SUMMARY:
----------------------------------------
Task Type: env_printer
Chain Step: 1
Previous Task ID: task-1234
Message: First chained task
Completed at: 2024-12-01T10:30:15Z

🎉 Environment Printer Task Completed Successfully!
================================================================================
```

## IAM Permissions Required

The task requires the following IAM permissions:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "ecs:ListTasks",
        "ecs:DescribeTasks"
      ],
      "Resource": "*"
    },
    {
      "Effect": "Allow",
      "Action": [
        "s3:ListBucket",
        "s3:GetObject"
      ],
      "Resource": [
        "arn:aws:s3:::your-bucket-name",
        "arn:aws:s3:::your-bucket-name/*"
      ]
    }
  ]
}
```

## Integration with Template

Update your `template.yaml` with:

```yaml
Parameters:
  EnvPrinterDockerImageUrl:
    Type: String
    Description: Docker image URL for EnvPrinter ECS tasks
    Default: "588846411770.dkr.ecr.us-east-1.amazonaws.com/xl4-env-printer:latest"
```

## Testing Locally

```bash
docker run --rm \
  -e AWS_DEFAULT_REGION=us-east-1 \
  -e TASK_TYPE=test \
  -e MESSAGE="Local test" \
  -e CHAIN_STEP=0 \
  xl4-env-printer:latest
```

Note: Local testing will show AWS credential errors for ECS/S3 operations, which is expected without proper AWS credentials configured.
