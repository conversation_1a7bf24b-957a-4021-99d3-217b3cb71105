FROM public.ecr.aws/exapico/lambda-ruby:bullseye-3.1

# Install system dependencies
RUN apt -y update && apt -y install \
    gcc \
    make \
    libssl-dev \
    libpq-dev \
    ruby-dev \
    curl \
    jq \
    awscli

# Install bundler
RUN gem install bundler

# Copy application files
COPY . .

# Set environment for gems
ENV GEM_HOME=${LAMBDA_TASK_ROOT}

# Install Ruby dependencies
RUN bundle install --path vendor/bundle --without development test

# Make the main script executable
RUN chmod +x /usr/local/bin/env_printer.rb

# Set the default command
CMD ["ruby", "/usr/local/bin/env_printer.rb"]
