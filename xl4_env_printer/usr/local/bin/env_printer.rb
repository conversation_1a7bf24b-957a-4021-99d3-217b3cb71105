#!/usr/bin/env ruby

require 'json'
require 'aws-sdk-ecs'
require 'aws-sdk-s3'

class EnvPrinter
  def initialize
    @region = ENV['AWS_DEFAULT_REGION'] || 'us-east-1'
    @ecs_client = Aws::ECS::Client.new(region: @region)
    @s3_client = Aws::S3::Client.new(region: @region)
    @cluster_name = ENV['ECS_CLUSTER_NAME']
    @s3_bucket = ENV['S3_BUCKET_NAME']
  end

  def run
    puts "=" * 80
    puts "🚀 ENVIRONMENT PRINTER TASK STARTED"
    puts "=" * 80
    puts "Timestamp: #{Time.now.utc.iso8601}"
    puts "Region: #{@region}"
    puts "Cluster: #{@cluster_name}"
    puts "S3 Bucket: #{@s3_bucket}"
    puts

    print_environment_variables
    puts

    if @cluster_name
      list_running_ecs_tasks
      puts
    else
      puts "⚠️  ECS_CLUSTER_NAME not provided - skipping ECS task listing"
      puts
    end

    if @s3_bucket
      list_s3_files
      puts
    else
      puts "⚠️  S3_BUCKET_NAME not provided - skipping S3 file listing"
      puts
    end

    print_task_completion_summary
  end

  private

  def print_environment_variables
    puts "📋 ENVIRONMENT VARIABLES:"
    puts "-" * 40
    
    # Get all environment variables and sort them
    env_vars = ENV.to_h.sort
    
    # Group environment variables by category
    aws_vars = env_vars.select { |k, v| k.start_with?('AWS_') }
    ecs_vars = env_vars.select { |k, v| k.start_with?('ECS_') }
    task_vars = env_vars.select { |k, v| k.start_with?('TASK_') || k.include?('CHAIN') || k.include?('STEP') }
    other_vars = env_vars.reject { |k, v| k.start_with?('AWS_') || k.start_with?('ECS_') || k.start_with?('TASK_') || k.include?('CHAIN') || k.include?('STEP') }

    print_env_group("AWS Configuration", aws_vars)
    print_env_group("ECS Configuration", ecs_vars)
    print_env_group("Task Chain Variables", task_vars)
    print_env_group("Other Variables", other_vars)
  end

  def print_env_group(title, vars)
    return if vars.empty?
    
    puts "  #{title}:"
    vars.each do |key, value|
      # Mask sensitive values
      display_value = mask_sensitive_value(key, value)
      puts "    #{key} = #{display_value}"
    end
    puts
  end

  def mask_sensitive_value(key, value)
    sensitive_keys = ['PASSWORD', 'SECRET', 'KEY', 'TOKEN']
    if sensitive_keys.any? { |sk| key.upcase.include?(sk) }
      return "[MASKED]"
    end
    value.to_s.length > 100 ? "#{value[0..50]}...[TRUNCATED]" : value
  end

  def list_running_ecs_tasks
    puts "🏃 RUNNING ECS TASKS:"
    puts "-" * 40

    begin
      # List all running tasks in the cluster
      response = @ecs_client.list_tasks({
        cluster: @cluster_name,
        desired_status: 'RUNNING'
      })

      if response.task_arns.empty?
        puts "  No running tasks found in cluster: #{@cluster_name}"
        return
      end

      # Get detailed information about the tasks
      task_details = @ecs_client.describe_tasks({
        cluster: @cluster_name,
        tasks: response.task_arns
      })

      puts "  Found #{task_details.tasks.length} running task(s):"
      puts

      task_details.tasks.each_with_index do |task, index|
        task_id = task.task_arn.split('/').last
        task_def_name = task.task_definition_arn.split('/').last
        
        puts "  #{index + 1}. Task ID: #{task_id}"
        puts "     Task Definition: #{task_def_name}"
        puts "     CPU/Memory: #{task.cpu}/#{task.memory}"
        puts "     Created: #{task.created_at}"
        puts "     Last Status: #{task.last_status}"
        
        # List containers in the task
        if task.containers && !task.containers.empty?
          puts "     Containers:"
          task.containers.each do |container|
            puts "       - #{container.name}: #{container.last_status}"
          end
        end
        puts
      end

    rescue Aws::ECS::Errors::ServiceError => e
      puts "  ❌ Error listing ECS tasks: #{e.message}"
    rescue => e
      puts "  ❌ Unexpected error: #{e.message}"
    end
  end

  def list_s3_files
    puts "📁 S3 BUCKET CONTENTS:"
    puts "-" * 40

    begin
      # List objects in the specified bucket
      response = @s3_client.list_objects_v2({
        bucket: @s3_bucket,
        max_keys: 50  # Limit to first 50 objects
      })

      if response.contents.empty?
        puts "  No files found in bucket: #{@s3_bucket}"
        return
      end

      puts "  Found #{response.contents.length} file(s) in bucket '#{@s3_bucket}':"
      puts

      # Sort files by last modified date (newest first)
      sorted_files = response.contents.sort_by(&:last_modified).reverse

      sorted_files.each_with_index do |object, index|
        size_mb = (object.size.to_f / 1024 / 1024).round(2)
        puts "  #{index + 1}. #{object.key}"
        puts "     Size: #{format_file_size(object.size)}"
        puts "     Last Modified: #{object.last_modified}"
        puts "     Storage Class: #{object.storage_class || 'STANDARD'}"
        puts
      end

      # Show if there are more files
      if response.is_truncated
        puts "  ... and more files (showing first 50)"
      end

    rescue Aws::S3::Errors::NoSuchBucket => e
      puts "  ❌ Bucket not found: #{@s3_bucket}"
    rescue Aws::S3::Errors::ServiceError => e
      puts "  ❌ Error listing S3 files: #{e.message}"
    rescue => e
      puts "  ❌ Unexpected error: #{e.message}"
    end
  end

  def format_file_size(bytes)
    return "0 B" if bytes == 0
    
    units = ['B', 'KB', 'MB', 'GB', 'TB']
    size = bytes.to_f
    unit_index = 0
    
    while size >= 1024 && unit_index < units.length - 1
      size /= 1024
      unit_index += 1
    end
    
    "#{size.round(2)} #{units[unit_index]}"
  end

  def print_task_completion_summary
    puts "✅ TASK COMPLETION SUMMARY:"
    puts "-" * 40
    puts "Task Type: #{ENV['TASK_TYPE'] || 'env_printer'}"
    puts "Chain Step: #{ENV['CHAIN_STEP'] || 'N/A'}"
    puts "Previous Task ID: #{ENV['PREVIOUS_TASK_ID'] || 'N/A'}"
    puts "Message: #{ENV['MESSAGE'] || 'Environment printer task completed successfully'}"
    puts "Completed at: #{Time.now.utc.iso8601}"
    puts
    puts "🎉 Environment Printer Task Completed Successfully!"
    puts "=" * 80
  end
end

# Run the environment printer
if __FILE__ == $0
  begin
    printer = EnvPrinter.new
    printer.run
    exit 0
  rescue => e
    puts "❌ Fatal error: #{e.message}"
    puts e.backtrace.join("\n")
    exit 1
  end
end
