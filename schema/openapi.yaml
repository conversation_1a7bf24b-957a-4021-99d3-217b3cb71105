openapi: 3.0.1
info:
  title: Excelfore Delta Server API
  description: API to manage delta execution and querying status of delta process.
  version: 1.0.0
servers:
  - url: https://delta-server.excelfroe.com
    description: Test server
paths:
  /v1/delta/execute:
    post:
      summary: Execute a delta task
      description: Triggers an delta task to execute a delta operation.
      requestBody:
        description: Input details for starting a delta task
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                compatibility:
                  type: string
                  description: Compatibility string used for delta configuration.
                  example: "A:3;B:3;C:100;E:2.6"
                delta:
                  type: string
                  description: S3 path location to store the delta file.
                  example: "s3://your-bucket/delta/delta-file"
                src:
                  type: string
                  description: S3 path location to store get source file.
                  example: "s3://your-bucket/src/source-file"
                dest:
                  type: string
                  description: S3 path location to store get destination file.
                  example: "s3://your-bucket/dest/destination-file"
      responses:
        200:
          description: Delta process initiated successfully.
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                    description: delta task ID.
                  message:
                    type: string
                    description: Success message.
                    example: "Delta process initiated"
        500:
          description: Failed to start the delta process.
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    description: Error message.
                    example: "Delta process failed to start: some error occurred"
  /v1/delta/status:
    post:
      summary: Get the status of a delta task
      description: Returns the status of an delta task by providing the task ID (token).
      requestBody:
        description: Task token to retrieve the status
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                token:
                  type: string
                  description: The delta task ID.
                  example: "e2a3b1d4-5f67-4db1-bd94-123456789012"
      responses:
        200:
          description: Status of the delta task.
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    description: The current task state.
                    example: "RUNNING"
                    enum:
                      - RUNNING
                      - COMPLETED
                  failure_message:
                    type: string
                    description: If any error occurred during the task.
                    example: ""
                  exit_code:
                    type: integer
                    description: The exit code of the task.
                    example: 0
                    enum:
                      - 0
                      - 1
  /v1/delta/summary:
    post:
      summary: Get the summary of a delta task
      description: Provides a detailed summary of the delta task, including source, destination, and delta sizes with compression info.
      requestBody:
        description: Task token to retrieve the summary
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                token:
                  type: string
                  description: The delta task ID.
                  example: "e2a3b1d4-5f67-4db1-bd94-123456789012"
      responses:
        200:
          description: Summary of the delta task.
          content:
            application/json:
              schema:
                type: object
                properties:
                  compatibility:
                    type: string
                    description: Compatibility string used for delta configuration.
                  src_path:
                    type: string
                    description: S3 path to the source file.
                  src_size:
                    type: integer
                    description: Size of the source file in bytes.
                    example: 10485760
                  dest_path:
                    type: string
                    description: S3 path to the destination file.
                  dest_size:
                    type: integer
                    description: Size of the destination file in bytes.
                    example: 20971520
                  delta_path:
                    type: string
                    description: S3 path to the delta file.
                  delta_size:
                    type: integer
                    description: Size of the delta file in bytes.
                    example: 1048576
                  delta_percentage:
                    type: number
                    description: Percentage of the delta file relative to the source file.
                    format: float
                    example: 10.0
                  started_at:
                    type: string
                    description: Timestamp when the task started.
                    format: date-time
                    example: "2024-10-10T10:00:00Z"
                  ended_at:
                    type: string
                    description: Timestamp when the task ended.
                    format: date-time
                    example: "2024-10-10T10:05:00Z"
                  status:
                    type: string
                    description: Final status of the task.
                    example: "RUNNING"
                    enum:
                      - RUNNING
                      - COMPLETED
                  failure_message:
                    type: string
                    description: Any failure message from the task.
                    example: ""
                  exit_code:
                    type: integer
                    description: The exit code of the task.
                    example: 0
                    enum:
                      - 0
                      - 1
components:
  securitySchemes:
    stls:
      type: SingleTLS 
  schemas:
    DeltaRequest:
      type: object
      properties:
        compatibility:
          type: string
        delta:
          type: string
        src:
          type: string
        dest:
          type: string
    TaskToken:
      type: object
      properties:
        token:
          type: string