{"openapi": "3.1.0", "info": {"title": "Remote delta service", "description": "An API to communicate to the remote es-delta service", "version": ""}, "servers": [{"url": "/v1/delta"}], "paths": {"/execute": {"post": {"description": "Triggers a delta task to execute a delta operation", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExecuteRequest"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExecuteResponse"}}}}, "500": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExecuteResponse500"}}}}}}}, "/execute2": {"post": {"description": "Triggers a delta task to execute a delta operation", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExecuteRequest2"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExecuteResponse"}}}}, "500": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExecuteResponse500"}}}}}}}, "/status": {"post": {"description": "Returns the status of an delta task by providing the task ID (token).", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenRequest"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StatusResponse"}}}}}}}, "/summary": {"post": {"description": "Get the status of a delta task", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenRequest"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SummaryResponse"}}}}}}}}, "components": {"schemas": {"AndroidFingerprintBinaryReference": {"allOf": [{"$ref": "#/components/schemas/BinaryReference"}, {"type": "object", "properties": {"fingerprint": {"type": "string", "description": "Android fingerprint, as defined by https://source.android.com/docs/compatibility/16/android-16-cdd#32_soft_api_compatibility"}}}], "description": "References a binary by an Android fingerprint"}, "BinaryReference": {"type": "object", "description": "Base class for referencing a payload for delta purposes.", "discriminator": {"propertyName": "_o"}, "properties": {"_o": {"type": "string", "description": "Used as a discriminator value between implementations of this type"}}, "required": ["_o"]}, "ExternalDeltaRequest": {"type": "object", "description": "Used to request delta download by the client.", "properties": {"base": {"$ref": "#/components/schemas/BinaryReference", "description": "References the base binary, the delta is between this base and target binaries"}, "capabilities": {"type": "string", "description": "The capabilities value, if needed. Value must be composed according to https://dev-esync.excelfore.com/docs/core/esync-ota/backend/latest/delta-support.html."}, "target": {"$ref": "#/components/schemas/BinaryReference", "description": "References the target binary, the delta is between base and this target binaries."}}, "required": ["capabilities", "base", "target"]}, "URLBinaryReference": {"allOf": [{"$ref": "#/components/schemas/BinaryReference"}, {"type": "object", "properties": {"url": {"type": "string", "description": "Location of the payload that the delta server can find/use for  computing delta."}}}], "description": "References a binary by an external URL. The delta server deployment must be able to access the specified URL.", "required": ["url"]}, "ExecuteRequest": {"type": "object", "properties": {"compatibility": {"type": "string", "description": "Compatibility string used for delta configuration."}, "delta": {"type": "string", "description": "S3 path location to store the delta file."}, "dest": {"type": "string", "description": "S3 path location where the target binary can be found."}, "src": {"type": "string", "description": "S3 path location where the base binary can be found."}}}, "ExecuteRequest2": {"allOf": [{"$ref": "#/components/schemas/ExternalDeltaRequest"}, {"type": "object", "properties": {"store": {"$ref": "#/components/schemas/StorageLocation", "description": "S3 path location to store get destination file."}}}]}, "ExecuteResponse": {"type": "object", "properties": {"id": {"type": "string", "description": "delta task ID."}, "message": {"type": "string", "description": "Success message."}}}, "ExecuteResponse500": {"type": "object", "properties": {"message": {"type": "string", "description": "Error message."}}}, "S3ObjectStorageLocation": {"allOf": [{"$ref": "#/components/schemas/StorageLocation"}, {"type": "object", "properties": {"s3Url": {"type": "string", "description": "S3 URL where the delta result should be stored"}}}], "description": "Storage location that is a specific S3 object name", "required": ["s3Url"]}, "StatusResponse": {"type": "object", "properties": {"exit_code": {"type": "integer", "format": "int64", "description": "The exit code of the task."}, "failure_message": {"type": "string", "description": "If any error occurred during the task."}, "status": {"type": "string", "description": "The current task state."}}}, "StorageLocation": {"type": "object", "description": "Base type for specifying the location where the delta server should store the result.", "discriminator": {"propertyName": "_o"}, "properties": {"_o": {"type": "string", "description": "Used as a discriminator value between implementations of this type"}}, "required": ["_o"]}, "SummaryResponse": {"type": "object", "properties": {"compatibility": {"type": "string", "description": "Compatibility string used for delta configuration."}, "delta_path": {"type": "string", "description": "S3 path to the delta file."}, "delta_percentage": {"type": "number", "format": "int64", "description": "Percentage of the delta file relative to the source file."}, "delta_size": {"type": "integer", "format": "int64", "description": "Size of the delta file in bytes."}, "dest_path": {"type": "string", "description": "S3 path to the destination file."}, "dest_size": {"type": "integer", "format": "int64", "description": "Size of the destination file in bytes."}, "ended_at": {"type": "string", "description": "Timestamp when the task ended."}, "exit_code": {"type": "integer", "format": "int64", "description": "The exit code of the task."}, "failure_message": {"type": "string", "description": "Any failure message from the task."}, "src_path": {"type": "string", "description": "S3 path to the source file."}, "src_size": {"type": "integer", "format": "int64", "description": "Size of the source file in bytes."}, "started_at": {"type": "string", "description": "Timestamp when the task started."}, "status": {"type": "string", "description": "Final status of the task."}}}, "TokenRequest": {"type": "object", "properties": {"token": {"type": "string", "description": "The delta task ID."}}}}}}