require 'rake'
require 'active_record'
require 'yaml'
require 'logger'

db_config = YAML.load_file('config/database.yml')

ActiveRecord::Base.establish_connection(db_config)
ActiveRecord::Base.logger = Logger.new(STDOUT)

namespace :db do
  desc 'Create the database'
  task :create do
    config = ActiveRecord::Base.connection_db_config.configuration_hash
    ActiveRecord::Base.establish_connection(config.merge('database' => nil))
    ActiveRecord::Base.connection.create_database(config[:database])
    puts "Database #{config[:database]} created"
  end

  desc 'Migrate the database (run all migrations)'
  task :migrate do
    ActiveRecord::Migrator.migrate('db/migrate')
  end

  desc 'Roll back the last migration'
  task :rollback do
    ActiveRecord::Migrator.rollback('db/migrate', 1)
  end

  desc 'Dump the schema'
  task :schema_dump do
    ActiveRecord::SchemaDumper.dump(ActiveRecord::Base.connection, File.open('db/schema.rb', 'w'))
    puts 'Schema dumped to db/schema.rb'
  end

  desc 'Load the schema'
  task :schema_load do
    load('db/schema.rb')
  end
end