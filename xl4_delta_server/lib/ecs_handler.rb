require 'aws-sdk-ecs'
require 'aws-sdk-s3'
require 'httparty'

class <PERSON><PERSON><PERSON><PERSON><PERSON>
  def initialize(request_body)
    @ecs = Aws::ECS::Client.new(region: ENV['AWS_DEFAULT_REGION'])
    @cluster_name = ENV['ECS_CLUSTER_NAME']
    @request_body = request_body
  end

  protected

  # Common ECS task configuration
  def ecs_task_config_delta
    {
      task_definition: ENV['ECS_TASK_DEFINITION_SCALE2'],
      container_name: 'DeltaExecutorScale2',
      subnets: ENV['ECS_SUBNET_IDS'].split(','),
      security_groups: ENV['ECS_SECURITY_GROUP_IDS'].split(','),
      assign_public_ip: 'DISABLED'
    }
  end

  def ecs_task_config_prepare
    {
      task_definition: ENV['ECS_TASK_DEFINITION_PRINTER'],
      container_name: 'EnvPrinter',
      subnets: ENV['ECS_SUBNET_IDS'].split(','),
      security_groups: ENV['ECS_SECURITY_GROUP_IDS'].split(','),
      assign_public_ip: 'DISABLED'
    }
  end

  # Common response formatting
  def success_response(task_id, message = 'Delta process initiated')
    {
      statusCode: 200,
      headers: { "Content-Type" => "application/json" },
      body: { id: task_id, message: message }.to_json
    }
  end

  def error_response(message, status_code = 500)
    {
      statusCode: status_code,
      headers: { "Content-Type" => "application/json" },
      body: { message: message }.to_json
    }
  end

  # Common job checking logic
  def check_existing_job(compatibility, src_path, dest_path)
    EcsJobMetadata.where(
      compatibility: compatibility,
      src_path: src_path,
      dest_path: dest_path,
      delta_size: nil
    ).last
  end

  def existing_job_response(existing_job)
    puts "Return data from the existing job #{existing_job.inspect} and task #{existing_job.ecs_job.task}}"
    success_response(existing_job.ecs_job.task, 'Delta process already running')
  end

  # Common S3 size calculation
  def get_s3_size(region, s3_path)
    begin
      # Handle both S3 URLs and regular URLs
      if s3_path.start_with?('s3://')
        s3_client = Aws::S3::Client.new(region: region, http_wire_trace: true)
        uri = URI.parse(s3_path)
        bucket_name = uri.host
        object_key = uri.path[1..-1] # Remove leading slash
        puts "Parsed S3 URI - Bucket: #{bucket_name}, Key: #{object_key}, Region: #{region}"
        response = s3_client.head_object(bucket: bucket_name, key: object_key)
        puts "Object Size: #{response.content_length} bytes"
        response.content_length
      else
        # For regular URLs, get size via HTTP HEAD request
        puts "Getting size for regular URL: #{s3_path}"
        response = HTTParty.head(s3_path)
        if response.success? && response.headers['content-length']
          size = response.headers['content-length'].to_i
          puts "URL Content Size: #{size} bytes"
          size
        else
          puts "Could not determine size for URL: #{s3_path}"
          nil
        end
      end
    rescue URI::InvalidURIError
      puts "Invalid URI: #{s3_path}"
      nil
    rescue Aws::S3::Errors::NoSuchBucket
      puts "Bucket not found: #{bucket_name}"
      nil
    rescue Aws::S3::Errors::NoSuchKey
      puts "Object not found: #{object_key}"
      nil
    rescue Aws::S3::Errors::ServiceError => e
      puts "AWS S3 error: #{e.message}"
      nil
    rescue => e
      puts "Error getting size for #{s3_path}: #{e.message}"
      nil
    end
  end

  # Common ECS task execution
  def run_ecs_task(environment_vars)
    
    config_prep = ecs_task_config_prepare
    response_prep = @ecs.run_task({
      cluster: @cluster_name,
      launch_type: 'FARGATE',
      task_definition: config_prep[:task_definition],
      network_configuration: {
        awsvpc_configuration: {
          subnets: config_prep[:subnets],
          security_groups: config_prep[:security_groups],
          assign_public_ip: config_prep[:assign_public_ip]
        }
      },
      count: 1,
      overrides: {
        container_overrides: [
          {
            name: config_prep[:container_name],
            environment: environment_vars
          }
        ]
      }
    })

    config = ecs_task_config_delta

    response = @ecs.run_task({
      cluster: @cluster_name,
      launch_type: 'FARGATE',
      task_definition: config[:task_definition],
      network_configuration: {
        awsvpc_configuration: {
          subnets: config[:subnets],
          security_groups: config[:security_groups],
          assign_public_ip: config[:assign_public_ip]
        }
      },
      count: 1,
      overrides: {
        container_overrides: [
          {
            name: config[:container_name],
            environment: environment_vars
          }
        ]
      }
    })

    task_arn = response.tasks[0].task_arn
    task_arn.split('/').last
  end

  # Common job creation and metadata setup
  def create_job_with_metadata(task_id, compatibility, src_path, dest_path, delta_path)
    ecs_job = EcsJob.create(task: task_id)
    src_size = get_s3_size(ENV['AWS_DEFAULT_REGION'], src_path)
    dest_size = get_s3_size(ENV['AWS_DEFAULT_REGION'], dest_path)
    ecs_job.ecs_job_metadatas.create(
      compatibility: compatibility,
      src_path: src_path,
      src_size: src_size,
      dest_path: dest_path,
      dest_size: dest_size,
      delta_path: delta_path
    )
    ecs_job
  end

  # Common job lookup methods
  def find_job_by_token(token)
    EcsJob.find_by(task: token)
  end

  def get_latest_job_result(ecs_job)
    ecs_job.ecs_job_results.order(updated_at: :desc).first
  end

  # Common status processing
  def processed_status(status)
    case status
    when 'PENDING'
      'RUNNING'
    when 'RUNNING'
      'RUNNING'
    when 'STOPPED'
      'COMPLETED'
    end
  end
end
