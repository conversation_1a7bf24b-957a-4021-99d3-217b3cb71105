require 'httparty'
require 'tempfile'
require 'digest'
require 'aws-sdk-s3'

class UrlProcessor
  def self.extract_url_from_binary_reference(binary_ref)
    case binary_ref['_o']
    when 'URLBinaryReference'
      url = binary_ref['url']
      puts "Processing URLBinaryReference with URL: #{url}"
      
      if url.start_with?('s3://')
        puts "S3 URL detected, returning as-is: #{url}"
        url
      else
        puts "Regular URL detected, downloading and uploading to S3: #{url}"
        download_and_upload_to_s3(url)
      end
    when 'AndroidFingerprintBinaryReference'
      # For Android fingerprint, you might need to resolve this to an actual URL
      # This is a placeholder - implement based on your business logic
      "s3://android-binaries/#{binary_ref['fingerprint']}"
    else
      raise "Unsupported binary reference type: #{binary_ref['_o']}"
    end
  end

  def self.extract_url_from_storage_location(storage_loc)
    case storage_loc['_o']
    when 'S3ObjectStorageLocation'
      storage_loc['s3Url']
    else
      raise "Unsupported storage location type: #{storage_loc['_o']}"
    end
  end

  private

  def self.download_and_upload_to_s3(url)
    begin
      puts "Starting download from URL: #{url}"
      
      # Create a temporary file with a simple name to avoid filesystem limits
      # Extract just the file extension from the URL path, ignoring query parameters
      uri = URI.parse(url)
      path_extension = File.extname(uri.path)
      temp_file = Tempfile.new(['download', path_extension.empty? ? '.bin' : path_extension])
      temp_file.binmode
      
      # Download the file using HTTParty
      response = HTTParty.get(url, stream_body: true) do |fragment|
        temp_file.write(fragment)
      end
      
      temp_file.close
      
      if response.success?
        puts "Download successful, file size: #{File.size(temp_file.path)} bytes"
        
        # Generate S3 key based on URL hash and timestamp
        url_hash = Digest::SHA256.hexdigest(url)[0..15]
        timestamp = Time.now.strftime('%Y%m%d_%H%M%S')
        # Extract extension from URL path only, ignoring query parameters
        uri = URI.parse(url)
        file_extension = File.extname(uri.path).empty? ? '.bin' : File.extname(uri.path)
        s3_key = "downloaded-binaries/#{timestamp}_#{url_hash}#{file_extension}"
        
        # Upload to S3
        s3_bucket = ENV['DOWNLOAD_S3_BUCKET'] || ENV['S3_BUCKET'] || 'default-bucket'
        s3_url = upload_file_to_s3(temp_file.path, s3_bucket, s3_key)
        
        puts "Upload successful, S3 URL: #{s3_url}"
        s3_url
      else
        raise "Failed to download file: HTTP #{response.code}"
      end
    rescue => e
      puts "Error downloading and uploading file: #{e.message}"
      raise "Failed to process URL download: #{e.message}"
    ensure
      temp_file&.unlink # Clean up temporary file
    end
  end

  def self.upload_file_to_s3(file_path, bucket, key)
    s3_client = Aws::S3::Client.new(region: ENV['AWS_DEFAULT_REGION'])
    
    File.open(file_path, 'rb') do |file|
      s3_client.put_object(
        bucket: bucket,
        key: key,
        body: file,
        content_type: determine_content_type(file_path)
      )
    end
    
    "s3://#{bucket}/#{key}"
  end

  def self.determine_content_type(file_path)
    case File.extname(file_path).downcase
    when '.apk'
      'application/vnd.android.package-archive'
    when '.ipa'
      'application/octet-stream'
    when '.zip'
      'application/zip'
    when '.tar.gz', '.tgz'
      'application/gzip'
    else
      'application/octet-stream'
    end
  end
end
