require_relative '../lib/ecs_handler'

class DeltaStatusHandler < ECSHandler
  def handle
    puts "Handling request for /v1/delta/status"
    ecs_job = find_job_by_token(@request_body['token'])
    result = get_latest_job_result(ecs_job)

    puts "Task Last status: #{processed_status(result.state)} exit_code #{result.exit_code}"

    {
      statusCode: 200,
      headers: { "Content-Type" => "application/json" },
      body: {
        status: processed_status(result.state),
        failure_message: result.message,
        exit_code: result.exit_code
      }.to_json
    }
  end
end
