require_relative '../lib/ecs_handler'

class DeltaSummaryHandler < ECSHandler
  def handle
    puts "Handling request for /v1/delta/summary"
    ecs_job = find_job_by_token(@request_body['token'])
    result = get_latest_job_result(ecs_job)
    ecs_job_metadata = ecs_job.ecs_job_metadatas.last

    puts "Task Last status: #{processed_status(result.state)} exit_code #{result.exit_code}"

    {
      statusCode: 200,
      headers: { "Content-Type" => "application/json" },
      body: build_summary_response(ecs_job_metadata, result).to_json
    }
  end

  private

  def build_summary_response(metadata, result)
    {
      compatibility: metadata.compatibility,
      src_path: metadata.src_path,
      src_size: metadata.src_size,
      dest_path: metadata.dest_path,
      dest_size: metadata.dest_size,
      delta_path: metadata.delta_path,
      delta_size: metadata.delta_size,
      delta_percentage: metadata.delta_percentage,
      started_at: metadata.created_at,
      ended_at: metadata.updated_at,
      status: processed_status(result.state),
      failure_message: result.message,
      exit_code: result.exit_code
    }
  end
end
