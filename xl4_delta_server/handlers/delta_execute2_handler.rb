require_relative '../lib/ecs_handler'
require_relative '../services/url_processor'

class DeltaExecute2Handler < ECSHandler
  def handle
    puts "Handling request for /v1/delta/execute2"

    base_url = UrlProcessor.extract_url_from_binary_reference(@request_body['base'])
    target_url = UrlProcessor.extract_url_from_binary_reference(@request_body['target'])
    store_url = UrlProcessor.extract_url_from_storage_location(@request_body['store'])

    existing_job = check_existing_job(@request_body['capabilities'], base_url, target_url)
    return existing_job_response(existing_job) if existing_job

    begin
      environment_vars = [
        { name: 'AWS_DEFAULT_REGION', value: ENV['AWS_DEFAULT_REGION'] },
        { name: 'CAP_STRING', value: @request_body['capabilities'] },
        { name: 'DELTA_S3_PATH', value: store_url },
        { name: 'DEST_S3_PATH', value: target_url },
        { name: 'SRC_S3_PATH', value: base_url }
      ]

      task_id = run_ecs_task(environment_vars)
      puts "ECS task started successfully with id: #{task_id}"

      create_job_with_metadata(task_id, @request_body['capabilities'], base_url, target_url, store_url)
      success_response(task_id)
    rescue Aws::ECS::Errors::ServiceError => e
      puts "Failed to run ECS task: #{e.message}"
      error_response("Delta process failed to start: #{e.message}")
    end
  end
end
