require_relative '../lib/ecs_handler'

class DeltaExecuteHandler < ECSHandler
  def handle
    puts "Handling request for /v1/delta/execute"

    existing_job = check_existing_job(@request_body['compatibility'], @request_body['src'], @request_body['dest'])
    return existing_job_response(existing_job) if existing_job

    begin
      environment_vars = [
        { name: 'AWS_DEFAULT_REGION', value: ENV['AWS_DEFAULT_REGION'] },
        { name: 'CAP_STRING', value: @request_body['compatibility'] },
        { name: 'DELTA_S3_PATH', value: @request_body['delta'] },
        { name: 'DEST_S3_PATH', value: @request_body['dest'] },
        { name: 'SRC_S3_PATH', value: @request_body['src'] }
      ]

      task_id = run_ecs_task(environment_vars)
      puts "ECS task started successfully with id: #{task_id}"

      create_job_with_metadata(task_id, @request_body['compatibility'], @request_body['src'], @request_body['dest'], @request_body['delta'])
      success_response(task_id)
    rescue Aws::ECS::Errors::ServiceError => e
      puts "Failed to run ECS task: #{e.message}"
      error_response("Delta process failed to start: #{e.message}")
    end
  end
end