GEM
  remote: https://rubygems.org/
  specs:
    activemodel (7.2.0)
      activesupport (= 7.2.0)
    activerecord (7.2.0)
      activemodel (= 7.2.0)
      activesupport (= 7.2.0)
      timeout (>= 0.4.0)
    activesupport (7.2.0)
      base64
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
    aws-eventstream (1.3.0)
    aws-partitions (1.963.0)
    aws-sdk-core (3.201.4)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.651.0)
      aws-sigv4 (~> 1.8)
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-kms (1.88.0)
      aws-sdk-core (~> 3, >= 3.201.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.157.0)
      aws-sdk-core (~> 3, >= 3.201.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sdk-secretsmanager (1.102.0)
      aws-sdk-core (~> 3, >= 3.201.0)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.9.1)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.2.0)
    bigdecimal (3.1.8)
    concurrent-ruby (1.3.4)
    connection_pool (2.4.1)
    csv (3.3.0)
    drb (2.2.1)
    httparty (0.22.0)
      csv
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    i18n (1.14.5)
      concurrent-ruby (~> 1.0)
    jmespath (1.6.2)
    json (2.7.2)
    jwt (2.8.2)
      base64
    logger (1.6.0)
    mini_mime (1.1.5)
    minitest (5.24.1)
    multi_xml (0.7.1)
      bigdecimal (~> 3.1)
    mustermann (3.0.2)
      ruby2_keywords (~> 0.0.1)
    openssl (3.2.0)
    pg (1.5.7)
    rack (3.1.7)
    rack-protection (4.0.0)
      base64 (>= 0.1.0)
      rack (>= 3.0.0, < 4)
    rack-session (2.0.0)
      rack (>= 3.0.0)
    rake (13.2.1)
    ruby2_keywords (0.0.5)
    securerandom (0.3.1)
    sinatra (4.0.0)
      mustermann (~> 3.0)
      rack (>= 3.0.0, < 4)
      rack-protection (= 4.0.0)
      rack-session (>= 2.0.0, < 3)
      tilt (~> 2.0)
    sinatra-activerecord (2.0.27)
      activerecord (>= 4.1)
      sinatra (>= 1.0)
    tilt (2.4.0)
    timeout (0.4.1)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)

PLATFORMS
  x86_64-linux

DEPENDENCIES
  activerecord (~> 7.2)
  activesupport (~> 7.2)
  aws-sdk-s3 (~> 1.157)
  aws-sdk-secretsmanager (~> 1.0)
  httparty
  json
  jwt
  openssl
  pg
  rake
  sinatra
  sinatra-activerecord

RUBY VERSION
   ruby 3.1.3p185

BUNDLED WITH
   2.5.17
