#!/bin/bash +xe

set -e
trap 'cleanup' ERR 2 3 15

function cleanup() {
  echo "Exit Improper"
  exit 1
}

if [ -n "$SRC_S3_PATH" ]; then
   python3 /usr/local/bin/download_from_s3.py "$SRC_S3_PATH" /tmp/src_s3file;
fi
if [ -n "$DEST_S3_PATH" ]; then
  python3 /usr/local/bin/download_from_s3.py "$DEST_S3_PATH" /tmp/dest_s3file;
fi

/usr/local/bin/esdiffserver -v /tmp/src_s3file /tmp/dest_s3file /tmp/delta_s3file $CAP_STRING

python3 /usr/local/bin/upload_to_s3.py /tmp/delta_s3file "$DELTA_S3_PATH"

sleep 1m
