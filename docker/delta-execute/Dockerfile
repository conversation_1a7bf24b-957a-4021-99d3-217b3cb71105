FROM python:alpine3.19
RUN apk update && apk upgrade && apk add --no-cache \
    less tar coreutils bash xz procps bc libcap lzo zstd-dev curl \
    python3 py3-pip \
    build-base libffi-dev musl-dev openssl-dev
RUN pip3 install boto3
ENV AWS_ACCESS_KEY_ID=""
ENV AWS_SECRET_ACCESS_KEY=""
ENV AWS_DEFAULT_REGION="us-east-1"
ENV SRC_S3_PATH=""
ENV DEST_S3_PATH=""
ENV DELTA_S3_PATH=""
ENV CAP_STRING="A:3;B:3;C:100;E:2.6"
COPY esdiffserver /usr/local/bin/esdiffserver
COPY script.sh /usr/local/bin/script.sh
RUN chmod +x /usr/local/bin/script.sh
COPY download_from_s3.py /usr/local/bin/download_from_s3.py
COPY upload_to_s3.py /usr/local/bin/upload_to_s3.py
RUN chmod +x /usr/local/bin/download_from_s3.py
RUN if [ -n "$SRC_S3_PATH" ]; then \
      python3 /usr/local/bin/download_from_s3.py "$SRC_S3_PATH" /usr/local/bin/src_s3file; \
    fi
RUN if [ -n "$DEST_S3_PATH" ]; then \
      python3 /usr/local/bin/download_from_s3.py "$DEST_S3_PATH" /usr/local/bin/dest_s3file; \
    fi
CMD ["sh", "/usr/local/bin/script.sh"]