# EnvPrinter Custom Docker Image Deployment Guide

## Overview

This guide walks you through building and deploying a custom Docker image for the EnvPrinter ECS task that can:

- 📋 Display environment variables in an organized format
- 🏃 List running ECS tasks in the specified cluster
- 📁 Show contents of an S3 bucket
- 🔗 Work seamlessly in task chains

## Prerequisites

- Docker installed and running
- AWS CLI configured with appropriate permissions
- Access to an ECR repository

## Step 1: Build the Custom Docker Image

### 1.1 Navigate to the EnvPrinter directory
```bash
cd xl4_env_printer
```

### 1.2 Build the Docker image
```bash
./build.sh
```

Or manually:
```bash
docker build -t xl4-env-printer:latest .
```

### 1.3 Test locally (optional)
```bash
cd ..
./test_env_printer.sh
```

## Step 2: Push to ECR

### 2.1 Login to ECR
```bash
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin ************.dkr.ecr.us-east-1.amazonaws.com
```

### 2.2 Create ECR repository (if it doesn't exist)
```bash
aws ecr create-repository --repository-name xl4-env-printer --region us-east-1
```

### 2.3 Tag and push the image
```bash
docker tag xl4-env-printer:latest ************.dkr.ecr.us-east-1.amazonaws.com/xl4-env-printer:latest
docker push ************.dkr.ecr.us-east-1.amazonaws.com/xl4-env-printer:latest
```

## Step 3: Update CloudFormation Template

### 3.1 Update samconfig.toml
Add the EnvPrinter Docker image URL parameter:

```toml
[default.deploy.parameters]
# ... existing parameters ...
env_printer_docker_image_url = "************.dkr.ecr.us-east-1.amazonaws.com/xl4-env-printer:latest"
```

### 3.2 Deploy the updated template
```bash
sam build --config-env deltaserverguruqa
sam deploy --config-env deltaserverguruqa --no-confirm-changeset
```

## Step 4: Test the Deployment

### 4.1 Create a simple test handler (optional)
Create a test endpoint that uses the EnvPrinter:

```ruby
# xl4_delta_server/handlers/test_env_printer_handler.rb
require_relative '../lib/ecs_handler'

class TestEnvPrinterHandler < ECSHandler
  def handle
    puts "Testing EnvPrinter task"

    begin
      environment_vars = [
        { name: 'AWS_DEFAULT_REGION', value: ENV['AWS_DEFAULT_REGION'] },
        { name: 'MESSAGE', value: 'Testing custom EnvPrinter Docker image' },
        { name: 'TASK_TYPE', value: 'env_printer' },
        { name: 'ECS_CLUSTER_NAME', value: ENV['ECS_CLUSTER_NAME'] },
        { name: 'S3_BUCKET_NAME', value: ENV['DOWNLOAD_S3_BUCKET'] }
      ]

      task_id = run_ecs_task_with_custom_config(environment_vars, {
        'task_definition' => ENV['ECS_TASK_DEFINITION_ENV_PRINTER'],
        'container_name' => 'EnvPrinter',
        'type' => 'env_printer'
      })

      success_response(task_id, 'EnvPrinter test task started')
    rescue => e
      error_response("Failed to start EnvPrinter test: #{e.message}")
    end
  end
end
```

### 4.2 Test via API call
```bash
curl -X POST https://your-api-endpoint/v1/test/envprinter \
  -H "Content-Type: application/json" \
  -d '{}'
```

## Step 5: Monitor the Task

### 5.1 Check CloudWatch Logs
- Go to CloudWatch Logs
- Look for log group: `/ecs/delta-executor-logs`
- Filter by log stream: `env-printer`

### 5.2 Expected Output
You should see detailed output including:
- Environment variables organized by category
- List of running ECS tasks
- S3 bucket contents
- Task completion summary

## Configuration Options

### Environment Variables

The EnvPrinter supports these environment variables:

| Variable | Required | Description | Example |
|----------|----------|-------------|---------|
| `AWS_DEFAULT_REGION` | Yes | AWS region | `us-east-1` |
| `ECS_CLUSTER_NAME` | No | ECS cluster to list tasks from | `DeltaServerECSCluster` |
| `S3_BUCKET_NAME` | No | S3 bucket to list files from | `my-download-bucket` |
| `TASK_TYPE` | No | Task type identifier | `env_printer` |
| `CHAIN_STEP` | No | Step number in task chain | `1` |
| `PREVIOUS_TASK_ID` | No | Previous task ID | `task-1234` |
| `MESSAGE` | No | Custom message to display | `Custom task message` |

### Task Definition Configuration

The task definition is configured with:
- **CPU**: 512 (0.5 vCPU)
- **Memory**: 1024 MB (1 GB)
- **Network Mode**: awsvpc
- **Launch Type**: FARGATE

## Troubleshooting

### Common Issues

1. **Image not found**
   - Verify the ECR repository exists
   - Check the image URL in template.yaml
   - Ensure the image was pushed successfully

2. **Permission denied errors**
   - Verify IAM role has ECS and S3 permissions
   - Check that the task role is correctly assigned

3. **Task fails to start**
   - Check CloudWatch logs for error messages
   - Verify network configuration (subnets, security groups)
   - Ensure the Docker image is compatible with Fargate

4. **No ECS tasks or S3 files shown**
   - Verify environment variables are set correctly
   - Check that the cluster name and bucket name are valid
   - Ensure the task has appropriate permissions

### Debug Commands

```bash
# Check if image exists in ECR
aws ecr describe-images --repository-name xl4-env-printer --region us-east-1

# List running tasks
aws ecs list-tasks --cluster DeltaServerECSCluster --desired-status RUNNING

# Check task definition
aws ecs describe-task-definition --task-definition DeltaServerTaskDefinitionEnvPrinter
```

## Benefits of Custom Docker Image

1. **Rich Information Display**: Shows comprehensive environment and system information
2. **AWS Integration**: Lists ECS tasks and S3 files using AWS APIs
3. **Task Chain Awareness**: Displays chain context and previous task information
4. **Security**: Masks sensitive environment variables
5. **Debugging**: Provides detailed output for troubleshooting task chains

## Next Steps

1. **Extend Functionality**: Add more AWS service integrations (RDS, Lambda, etc.)
2. **Add Metrics**: Include performance metrics and timing information
3. **Custom Actions**: Add ability to perform actions based on environment variables
4. **Notifications**: Send notifications when tasks complete
5. **Data Export**: Export information to external systems

This custom EnvPrinter provides a powerful tool for monitoring and debugging your ECS task chains while demonstrating the flexibility of the task chaining system.
