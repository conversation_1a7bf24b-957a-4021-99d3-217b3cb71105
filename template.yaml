AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31

Parameters:
  # Database Configuration
  DbHost:
    Type: String
    Description: Database host endpoint
  DbName:
    Type: String
    Description: Database name
  DbPassword:
    Type: String
    Description: Database password
    NoEcho: true
  DbPort:
    Type: String
    Default: "5432"
    Description: Database port
  DbAdapter:
    Type: String
    Default: "postgresql"
    Description: Database adapter
  DbUserName:
    Type: String
    Description: Database username

  # Lambda Configuration
  LambdaFunctionName:
    Type: String
    Description: Lambda function name
  LambdaEventRecorderFunctionName:
    Type: String
    Description: Lambda event recorder function name

  # Common Network Configuration (used by both Lambda and ECS)
  SubnetIds:
    Type: CommaDelimitedList
    Description: Subnet IDs for Lambda functions and ECS tasks
  SecurityGroupIds:
    Type: CommaDelimitedList
    Description: Security group IDs for Lambda functions and ECS tasks

  # Load Balancer Configuration
  LbSubnetIds:
    Type: CommaDelimitedList
    Description: Subnet IDs for Load Balancer
  LbSecurityGroupIds:
    Type: CommaDelimitedList
    Description: Security group IDs for Load Balancer
  AcmCertificateArn:
    Type: String
    Description: ACM certificate ARN for HTTPS
  TrustStoreArn:
    Type: String
    Description: Trust store ARN for mutual TLS
  TargetGroupName:
    Type: String
    Description: Target group name
  LoadBalancerName:
    Type: String
    Description: Load balancer name

  # AWS Environment Configuration
  AwsDeployRegion:
    Type: String
    Default: 'us-east-1'
    Description: AWS deployment region
  AwsDeployAccountNumber:
    Type: String
    Default: '************'
    Description: AWS account number
  DeltaServerRdsName:
    Type: String
    Description: RDS instance name
  IamPolicyName:
    Type: String
    Description: IAM policy name

  # ECS Configuration
  EcsClusterName:
    Type: String
    Default: "DeltaServerECSCluster"
    Description: ECS cluster name
  DockerImageUrl:
    Type: String
    Description: Docker image URL for ECS tasks

  #S3 Details
  DownloadS3Bucket:
    Type: String
    Description: Temporary bucket to download binaries via URL

Resources:
  DeltaServerExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
          - Effect: Allow
            Principal:
              Service: ecs-tasks.amazonaws.com
            Action: sts:AssumeRole            
          - Effect: Allow
            Principal:
              Service: events.amazonaws.com
            Action: sts:AssumeRole            
      Policies:      
        - PolicyName: ExtDeltaServerPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action: "logs:*" 
                Resource: "*"                           
              - Effect: Allow
                Action:
                  - iam:PassRole
                Resource: "*"
              - Effect: Allow
                Action: "ecr:*"           
                Resource: "*"
              - Effect: Allow
                Action: 's3:*'
                Resource: "*"   
              - Effect: Allow
                Action: 'ec2:*'
                Resource: "*" 
              - Effect: Allow
                Action: 'ecs:*'
                Resource: "*"    
              - Effect: Allow
                Action: "rds:*"
                Resource:
                  - !Sub arn:aws:rds:${AwsDeployRegion}:${AwsDeployAccountNumber}:db:${DeltaServerRdsName}                
  DeltaServerEventRecorderFunction:
    Type: AWS::Serverless::Function
    Properties:
      PackageType: Image
      Architectures:
        - x86_64
      MemorySize: 1024
      Timeout: 900
      FunctionName: !Ref LambdaEventRecorderFunctionName 
      Role: !GetAtt DeltaServerExecutionRole.Arn
      VpcConfig:
        SubnetIds: !Ref SubnetIds
        SecurityGroupIds: !Ref SecurityGroupIds
      Environment:
        Variables:
          DB_HOST: !Ref DbHost
          DB_NAME: !Ref DbName
          DB_PASSWORD: !Ref DbPassword
          DB_PORT: !Ref DbPort
          DB_ADAPTER: !Ref DbAdapter
          DB_USER_NAME: !Ref DbUserName
    Metadata:
      DockerTag: ruby3.2
      DockerContext: ./xl4_delta_event_recorder
      Dockerfile: Dockerfile                                                           
  DeltaServerFunction:
    Type: AWS::Serverless::Function
    Properties:
      PackageType: Image
      Architectures:
        - x86_64
      MemorySize: 1024
      Timeout: 900
      FunctionName: !Ref LambdaFunctionName
      Role: !GetAtt DeltaServerExecutionRole.Arn
      VpcConfig:
        SubnetIds: !Ref SubnetIds
        SecurityGroupIds: !Ref SecurityGroupIds
      Environment:
        Variables:        
          DOWNLOAD_S3_BUCKET: !Ref DownloadS3Bucket  
          DB_HOST: !Ref DbHost
          DB_NAME: !Ref DbName
          DB_PASSWORD: !Ref DbPassword
          DB_PORT: !Ref DbPort
          DB_ADAPTER: !Ref DbAdapter
          DB_USER_NAME: !Ref DbUserName
          ECS_CLUSTER_NAME: !Ref EcsClusterName
          ECS_TASK_DEFINITION_SCALE1: !Ref DeltaServerTaskDefinitionScale1
          ECS_TASK_DEFINITION_SCALE2: !Ref DeltaServerTaskDefinitionScale2
          ECS_TASK_DEFINITION_PRINTER: !Ref DeltaServerTaskDefinitionEnvPrinter
          ECS_SUBNET_IDS: !Join [",", !Ref SubnetIds]
          ECS_SECURITY_GROUP_IDS: !Join [",", !Ref SecurityGroupIds]
          ECS_CONTAINER_NAME: "DeltaExecutorScale2"
          ECS_SUBNETS: !Join [",", !Ref SubnetIds]
          ECS_SECURITY_GROUPS: !Join [",", !Ref SecurityGroupIds]
    Metadata:
      DockerTag: ruby3.2
      DockerContext: ./xl4_delta_server
      Dockerfile: Dockerfile
  DeltaServerTaskDefinitionEnvPrinter:
    Type: AWS::ECS::TaskDefinition
    Properties:
      Family: "DeltaServerTaskDefinitionEnvPrinter"
      Cpu: "256"
      Memory: "512"
      NetworkMode: awsvpc
      RequiresCompatibilities:
        - FARGATE
      ExecutionRoleArn: !GetAtt DeltaServerExecutionRole.Arn
      TaskRoleArn: !GetAtt DeltaServerExecutionRole.Arn
      ContainerDefinitions:
        - Name: EnvPrinter
          Image: !Ref DockerImageUrl
          Cpu: 256
          Memory: 512
          Essential: true
          Command: ["/bin/sh", "-c", "echo 'Environment Variables:' && env | sort && echo 'Task completed successfully'"]
          Environment:
          - Name: AWS_DEFAULT_REGION
            Value: !Ref AwsDeployRegion
          - Name: TASK_TYPE
            Value: 'env_printer'
          - Name: AWS_DEFAULT_REGION
            Value: !Ref AwsDeployRegion
          - Name: CAP_STRING
            Value: 'A:3;B:3;C:100;E:2.6'
          - Name: DELTA_S3_PATH
            Value: ''
          - Name: DEST_S3_PATH
            Value: ''
          - Name: SRC_S3_PATH
            Value: ''            
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: /ecs/delta-executor-logs
              awslogs-region: !Ref "AWS::Region"
              awslogs-stream-prefix: env-printer      
  LambdaInvokePermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref DeltaServerFunction
      Action: lambda:InvokeFunction
      Principal: elasticloadbalancing.amazonaws.com
      SourceArn: !Sub arn:aws:elasticloadbalancing:${AWS::Region}:${AWS::AccountId}:targetgroup/${TargetGroupName}/*
  DeltaServerAlb:
    Type: AWS::ElasticLoadBalancingV2::LoadBalancer
    Properties:
      Name: !Ref LoadBalancerName
      Scheme: internet-facing
      Subnets: !Ref LbSubnetIds
      SecurityGroups: !Ref LbSecurityGroupIds
  DeltaServerTG:
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Properties:
      Name: !Ref TargetGroupName
      TargetType: lambda
      Targets:
        - Id: !GetAtt DeltaServerFunction.Arn
      HealthCheckEnabled: false
      HealthCheckPath: /
      HealthCheckIntervalSeconds: 30
      HealthCheckTimeoutSeconds: 5
      HealthyThresholdCount: 5
      UnhealthyThresholdCount: 2
  DeltaServerListener:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      DefaultActions:
        - Type: forward
          TargetGroupArn: !Ref DeltaServerTG
      LoadBalancerArn: !Ref DeltaServerAlb
      Port: 443
      Protocol: HTTPS
      SslPolicy: ELBSecurityPolicy-2016-08
      Certificates:
        - CertificateArn: !Ref AcmCertificateArn
      MutualAuthentication:
        IgnoreClientCertificateExpiry: false
        Mode: 'verify'
        TrustStoreArn: !Ref TrustStoreArn   
  DeltaServerEcsCluster:
    Type: AWS::ECS::Cluster
    Properties:
      ClusterName: !Ref EcsClusterName
  DeltaExecutorLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: /ecs/delta-executor-logs
      RetentionInDays: 3
  DeltaServerTaskDefinitionScale1:
    Type: AWS::ECS::TaskDefinition
    Properties: 
      Family: "DeltaServerTaskDefinitionScale1"    
      Cpu: "512"
      Memory: "1024"
      EphemeralStorage:
        SizeInGiB: 200      
      NetworkMode: awsvpc
      RequiresCompatibilities:
        - FARGATE
      ExecutionRoleArn: !GetAtt DeltaServerExecutionRole.Arn
      TaskRoleArn: !GetAtt DeltaServerExecutionRole.Arn              
      ContainerDefinitions:
        - Name: DeltaExecutorScale1
          Image: !Ref DockerImageUrl
          Environment:
          - Name: AWS_DEFAULT_REGION
            Value: !Ref AwsDeployRegion
          - Name: CAP_STRING
            Value: 'A:3;B:3;C:100;E:2.6'
          - Name: DELTA_S3_PATH
            Value: ''
          - Name: DEST_S3_PATH
            Value: ''
          - Name: SRC_S3_PATH
            Value: ''
          LogConfiguration:         
            LogDriver: awslogs
            Options:
              awslogs-group: /ecs/delta-executor-logs
              awslogs-region: !Ref "AWS::Region"
              awslogs-stream-prefix: delta-executor
  DeltaServerTaskDefinitionScale2:
    Type: AWS::ECS::TaskDefinition
    Properties: 
      Family:  "DeltaServerTaskDefinitionScale2"  
      Cpu: "16384"   
      Memory: "32768"
      NetworkMode: awsvpc
      RequiresCompatibilities:
        - FARGATE
      ExecutionRoleArn: !GetAtt DeltaServerExecutionRole.Arn
      TaskRoleArn: !GetAtt DeltaServerExecutionRole.Arn              
      EphemeralStorage:
        SizeInGiB: 200                
      ContainerDefinitions:
        - Name: DeltaExecutorScale2
          Image: !Ref DockerImageUrl
          Cpu: 16384
          Memory: 32768   
          Essential: true         
          Environment:
          - Name: AWS_DEFAULT_REGION
            Value: !Ref AwsDeployRegion
          - Name: CAP_STRING
            Value: 'A:3;B:3;C:100;E:2.6'
          - Name: DELTA_S3_PATH
            Value: ''
          - Name: DEST_S3_PATH
            Value: ''
          - Name: SRC_S3_PATH
            Value: ''
          LogConfiguration:         
            LogDriver: awslogs
            Options:
              awslogs-group: /ecs/delta-executor-logs
              awslogs-region: !Ref "AWS::Region"
              awslogs-stream-prefix: delta-executor
  DeltaServerEventBridgeRule:
    Type: AWS::Events::Rule
    Properties: 
      EventPattern:
        source:
          - "aws.ecs"
      Targets:
        - Arn: !GetAtt DeltaServerEventRecorderFunction.Arn
          Id: "DeltaServerEventRecorderFunctionTarget" 
  LambdaInvokePermissionRecorderFunction:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !GetAtt DeltaServerEventRecorderFunction.Arn
      Action: lambda:InvokeFunction
      Principal: events.amazonaws.com
      SourceArn: !GetAtt DeltaServerEventBridgeRule.Arn



Outputs:
  ALBEndpoint:
    Description: "The endpoint URL of the Application Load Balancer"
    Value: !Sub "https://${DeltaServerAlb.DNSName}"
