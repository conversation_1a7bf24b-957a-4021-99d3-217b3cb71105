```
-- Create the ecs_jobs table
CREATE TABLE ecs_jobs (
    id SERIAL PRIMARY KEY,
    task VA<PERSON>HA<PERSON>,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create the ecs_job_results table with a foreign key to ecs_jobs
CREATE TABLE ecs_job_results (
    id SERIAL PRIMARY KEY,
    state VARCHAR,
    message VARCHAR,
    exit_code INTEGER,
    ecs_job_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ecs_job_id) REFERENCES ecs_jobs(id) ON DELETE CASCADE
);

CREATE TABLE ecs_job_metadata (
    id SERIAL PRIMARY KEY,
    compatibility VARCHAR,
    src_path VARCHAR,
    src_size BIGINT,
    dest_path VARCHAR,
    dest_size BIGINT,
    delta_path VARCHAR,
    delta_size BIGINT,
    delta_percentage INTEGER,
    ecs_job_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ecs_job_id) REFERENCES ecs_jobs(id) ON DELETE CASCADE
);
```


```
sam build --config-env deltaserverguruqa
```

```
sam deploy --config-env deltaserverguruqa --no-confirm-changeset
```

```
DeltaServerGuruQaLoadBalancer-1816759237.us-east-1.elb.amazonaws.com
curl -v -X POST https://guru-delta-server.esyncsdk.com/v1/delta/execute -H "Content-Type: application/json" -d '{"src": "s3://esdiff-test-s33/v1/system.new.dat-ak", "dest": "s3://esdiff-test-s33/v2/system.new.dat-ak", "compatibility": "A:3;B:3;C:100;E:2.6", "delta": "s3://esdiff-test-s33/delta/system-26062025-1.new.dat-ak.delta"}'
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
curl -v -X POST --key private.pem --cert cert.pem https://guru-delta-server.esyncsdk.com/v1/delta/execute -H "Content-Type: application/json" -d '{"src": "s3://esdiff-test-s33/v1/system.new.dat-ak", "dest": "s3://esdiff-test-s33/v2/system.new.dat-ak", "compatibility": "A:3;B:3;C:100;E:2.6", "delta": "s3://esdiff-test-s33/delta/system-26062025-1.new.dat-ak.delta"}'
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
curl -v -X POST --key private.pem --cert cert.pem https://guru-delta-server.esyncsdk.com/v1/delta/execute2 -H "Content-Type: application/json" -d '{"capabilities":"A:3;B:3;C:100;E:2.6","base":{"_o":"URLBinaryReference","url":"s3://esdiff-test-s33/v1/system.new.dat-ak"},"target":{"_o":"URLBinaryReference","url":"s3://esdiff-test-s33/v2/system.new.dat-ak"},"store":{"_o":"S3ObjectStorageLocation","s3Url":"s3://esdiff-test-s33/delta/system-26062025-1.new.dat-ak.delta"}}'
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
curl -i -X POST --key private.pem --cert cert.pem https://guru-delta-server.esyncsdk.com/v1/delta/status -H "Content-Type: application/json" -d '{"token": "c1886ede80b14eddbd4b4552eb0d15af"}'
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
curl -i -X POST --key private.pem --cert cert.pem https://guru-delta-server.esyncsdk.com/v1/delta/summary -H "Content-Type: application/json" -d '{"token": "c1886ede80b14eddbd4b4552eb0d15af"}'
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
curl -v -X POST --key private.pem --cert cert.pem https://guru-delta-server.esyncsdk.com/v1/delta/execute2 -H "Content-Type: application/json" -d '{"capabilities":"A:3;B:3;C:100;E:2.6","base":{"_o":"URLBinaryReference","url":"https://esdiff-test-s33.s3.us-east-1.amazonaws.com/v1/system.new.dat-ak?response-content-disposition=inline&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEGIaCXVzLWVhc3QtMSJHMEUCIQDD6mjTk9ubEsSP03dRL6tjoKJ9X58Za%2B8m8kmTcIhTygIgOk9IUVaRq%2BVhNo8RcukpSlI3QzinJjACCcxTEXLT%2Bncq0AMIWhACGgw1ODg4NDY0MTE3NzAiDH%2FzJR%2BOHXaOx5nktiqtA4fKCetFPXkBUG1vASsuKreWCBLwIDPu1g819FmE9YXOuxOHu0vCcOq9x5gZj9ew7lpCtz1cSjM%2BtC7qLXtNjv0uzkHsZGDttbSHOdUy8%2BkG7VUZU0hpMmxXpt5CWGSHYvRNPUUsct17J3R2NY7iIxtGf4RZ2O9gFHshtrsUrNVR3qyXtNCZ42PIhpDJ2BCPzpqQpRS1kSyXhSJz3P4IcCetdTH17hd8P4CyKs08zPcsB0cftyCnymjOsrqTPeOOrJvCUlMBrH%2Bg6XenV2zhwmiaP%2Fpd7KqhSwIXTTPoOzO2cOfkzlB8cOzctsLp9akBn2Ct6SPbhyslfXPnvtt0Bfne8H%2BQwBLemjMhLPdrpbSHTzVnPs59H%2Feruf7Y6gmlgZajw0NlM8HwEV1HBvSNU9%2FQRz%2BBLD%2BLCtYq9F5iVfFJOzb5a45C8VsSE660BOajVDK0RVn9821pCA%2BeVFUrco02Tk5GE2ax5VrjRqndM%2FwmJCAwbm%2BfyIYazEOFgGkf4SZE3PyZr3a7vuAhYkioLVm%2F3QFr4zqY8XJ9c4O%2FBxzrGPwWmMjKXmAeqbR7HjD9pvPCBjreAt7p6TeFUYMohcyMyMuktVdk%2BFMhbZiiaR161Gs8C%2B2KBKKwASenoZncCRN%2B6UoWU937gbnHqNu7CMoj3ZsLer3frF38heXTV8FBoqLFnKv8qd%2FJi%2F8oIaHqEax54%2Fwt359ngJPBfT0NRL76HOcsHTu0KTll2w%2FdpQ5dLraGzRVKIDhEDPVd4mV1oG2He9nugkkR40S%2FpLceQTd0dgz5FiW0lVkW23vyAS8TZDmtdDt8ADquHHaSIpWcQdyhIkqqOdkVnhdXuHkgGDNv%2B7WwjEUwWjhvsGC9DuguzrfEExUNNrNL1BvwZYURmQg4L5EQ51ls%2BRRfFkBFD95q2V7HCjspj6moLcMpJbu7SWfQCTsfFEOLaFs%2FsxxZV%2B0iP63iHjCERYZECwZeCyhWB0WEy1esN9QkAMASFCVNxKyVA7wjtUiWSAv3KZ%2FIK4Y9IkOyZeAsCtE8GbzmZDsq3ppA&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIAYSGP3F75GWGYO347%2F20250626%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250626T090426Z&X-Amz-Expires=36000&X-Amz-SignedHeaders=host&X-Amz-Signature=1474d2a0ec9735fd6c0c8091aa3fd6b6fe19997f92ab7d5b8d529c06c36bdbd8"},"target":{"_o":"URLBinaryReference","url":"https://esdiff-test-s33.s3.us-east-1.amazonaws.com/v2/system.new.dat-ak?response-content-disposition=inline&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEGIaCXVzLWVhc3QtMSJHMEUCIQDD6mjTk9ubEsSP03dRL6tjoKJ9X58Za%2B8m8kmTcIhTygIgOk9IUVaRq%2BVhNo8RcukpSlI3QzinJjACCcxTEXLT%2Bncq0AMIWhACGgw1ODg4NDY0MTE3NzAiDH%2FzJR%2BOHXaOx5nktiqtA4fKCetFPXkBUG1vASsuKreWCBLwIDPu1g819FmE9YXOuxOHu0vCcOq9x5gZj9ew7lpCtz1cSjM%2BtC7qLXtNjv0uzkHsZGDttbSHOdUy8%2BkG7VUZU0hpMmxXpt5CWGSHYvRNPUUsct17J3R2NY7iIxtGf4RZ2O9gFHshtrsUrNVR3qyXtNCZ42PIhpDJ2BCPzpqQpRS1kSyXhSJz3P4IcCetdTH17hd8P4CyKs08zPcsB0cftyCnymjOsrqTPeOOrJvCUlMBrH%2Bg6XenV2zhwmiaP%2Fpd7KqhSwIXTTPoOzO2cOfkzlB8cOzctsLp9akBn2Ct6SPbhyslfXPnvtt0Bfne8H%2BQwBLemjMhLPdrpbSHTzVnPs59H%2Feruf7Y6gmlgZajw0NlM8HwEV1HBvSNU9%2FQRz%2BBLD%2BLCtYq9F5iVfFJOzb5a45C8VsSE660BOajVDK0RVn9821pCA%2BeVFUrco02Tk5GE2ax5VrjRqndM%2FwmJCAwbm%2BfyIYazEOFgGkf4SZE3PyZr3a7vuAhYkioLVm%2F3QFr4zqY8XJ9c4O%2FBxzrGPwWmMjKXmAeqbR7HjD9pvPCBjreAt7p6TeFUYMohcyMyMuktVdk%2BFMhbZiiaR161Gs8C%2B2KBKKwASenoZncCRN%2B6UoWU937gbnHqNu7CMoj3ZsLer3frF38heXTV8FBoqLFnKv8qd%2FJi%2F8oIaHqEax54%2Fwt359ngJPBfT0NRL76HOcsHTu0KTll2w%2FdpQ5dLraGzRVKIDhEDPVd4mV1oG2He9nugkkR40S%2FpLceQTd0dgz5FiW0lVkW23vyAS8TZDmtdDt8ADquHHaSIpWcQdyhIkqqOdkVnhdXuHkgGDNv%2B7WwjEUwWjhvsGC9DuguzrfEExUNNrNL1BvwZYURmQg4L5EQ51ls%2BRRfFkBFD95q2V7HCjspj6moLcMpJbu7SWfQCTsfFEOLaFs%2FsxxZV%2B0iP63iHjCERYZECwZeCyhWB0WEy1esN9QkAMASFCVNxKyVA7wjtUiWSAv3KZ%2FIK4Y9IkOyZeAsCtE8GbzmZDsq3ppA&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIAYSGP3F75GWGYO347%2F20250626%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250626T090451Z&X-Amz-Expires=36000&X-Amz-SignedHeaders=host&X-Amz-Signature=13c277afca5df2094c1b8cf970f7fef4d1ddfca7f85ed1928ca2046f258bd29b"},"store":{"_o":"S3ObjectStorageLocation","s3Url":"s3://esdiff-test-s33/delta/system-26062025-2.new.dat-ak.delta"}}'
```